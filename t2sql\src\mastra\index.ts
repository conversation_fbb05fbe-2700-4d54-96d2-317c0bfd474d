import { <PERSON><PERSON> } from '@mastra/core/mastra';
import { LibSQLStore } from '@mastra/libsql';
import { PinoLogger } from '@mastra/loggers';
import { sqlAgent } from './agents/sql-agent';
import { databaseQueryWorkflow } from './workflows/database-query-workflow';

export const mastra = new Mastra({
  agents: { sqlAgent },
  workflows: {
    databaseQueryWorkflow,
  },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
    url: ':memory:',
  }),
  logger: new PinoLogger({
    name: '<PERSON><PERSON>',
    level: 'info',
  }),
});
