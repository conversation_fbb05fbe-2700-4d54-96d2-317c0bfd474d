{"name": "template-text-to-sql", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "description": "A Mastra workflow system for database introspection and natural language to SQL conversion. Features PostgreSQL schema analysis, AI-powered query generation, safe SQL execution, and interactive workflows for database operations.", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@mastra/core": "latest", "@mastra/libsql": "latest", "@mastra/loggers": "latest", "@mastra/memory": "latest", "ai": "^4.3.17", "csv-parse": "^5.6.0", "dotenv": "^17.0.1", "pg": "^8.16.3", "zod": "^3.25.75"}, "devDependencies": {"@types/node": "^24.0.10", "@types/pg": "^8.15.4", "mastra": "latest", "typescript": "^5.8.3"}}